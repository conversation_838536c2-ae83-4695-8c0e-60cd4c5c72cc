# App Store Submission Preparation Checklist

This document outlines the necessary steps to prepare the Verbalyze app for submission to both the Apple App Store and Google Play Store, with special attention to compliance requirements.

## 🚨 Medical and Mental Health Terminology Considerations

Apple and Google have specific guidelines for apps that could be interpreted as providing medical or mental health services. Based on Apple's App Store Review Guidelines (section 1.4.1) and Google Play's Health Content and Services policy:

- [x] **Review and modify terminology related to medical or mental health services**

  Apps that appear to provide medical advice, diagnosis, or treatment require greater scrutiny and may need:
  - [x] Clear disclaimers that the app is not a substitute for professional medical care
  - [x] Reminders to users to check with a doctor before making medical decisions
  - [x] Accurate representation of the app's capabilities without overstating health benefits

  To reduce the risk of extended review or rejection, consider:

  - [x] Rename database tables and fields to use more neutral terminology:
    - [x] Rename `therapist_personas` table to `companion_personas` in database
    - [x] Rename `therapist_id` field to `companion_id` in all tables
    - [x] Update all references in SQL queries and database code

  - [x] Update code references:
    - [x] `src/hooks/useChat.ts`: Modify system prompt to avoid therapeutic claims
    - [x] `src/lib/therapistProfiles.ts`: Rename file to `companionProfiles.ts` and update references
    - [x] `app/all-therapists.tsx`: Rename to `all-companions.tsx` and update content
    - [x] `src/screens/AllTherapistsScreen.tsx`: Rename to `AllCompanionsScreen.tsx`
    - [x] `src/screens/ChatScreen.tsx`: Update therapist references to companion
    - [x] `src/components/ChatHeader.tsx`: Update accessibility hints and labels
    - [x] `src/components/ChatInput.tsx`: Update accessibility hints

  - [x] Update UI text to focus on emotional wellbeing rather than therapy:
    - [x] Change "therapists" to "companions" or "guides" in all UI components
    - [x] Change "therapy" to "conversation" or "support session" in all text
    - [x] Update references to "mental health" to "emotional wellbeing" or "personal growth"

- [x] **Update system prompts in useChat.ts**
  - [x] Replace:
    ```javascript
    const systemPrompt = `You are a therapist with the following characteristics:
    - Persona: ${therapistData.persona || 'A supportive and empathetic therapist'}
    - Style: ${therapistData.style || 'Conversational and supportive'}
    - Specialty: ${therapistData.specialty || 'General therapy'}
    ```

    With:
    ```javascript
    const systemPrompt = `You are a supportive guide with the following characteristics:
    - Persona: ${therapistData.persona || 'A supportive and empathetic guide'}
    - Style: ${therapistData.style || 'Conversational and supportive'}
    - Specialty: ${therapistData.specialty || 'General support'}
    ```

- [x] **Update database structure**
  - [x] Modify `database_structure.sql` to rename tables and fields
  - [x] Update `update_therapist_images.sql` to use new terminology
  - [x] Create migration script to rename existing tables and fields

## 📱 App Metadata and Configuration

- [x] **Update app description in app.config.js**
  - [x] Update the description field:
    ```javascript
    description: "Verbalyze is a private, AI-powered chat companion that supports mental clarity and emotional reflection through secure conversations.",
    ```
    to:
    ```javascript
    description: "Verbalyze is a private, AI-powered chat companion that supports personal growth and emotional wellbeing through secure conversations.",
    ```
  - [x] Ensure the app description clearly states this is NOT a medical or therapeutic service
  - [x] Add disclaimer that the app is for informational and educational purposes only

- [x] **Update README.md**
  - [x] Change the tagline from:
    ```
    **Secure, AI-powered therapy companion app offering chat-based mental health support.**
    ```
    to:
    ```
    **Secure, AI-powered conversation companion app offering chat-based emotional wellbeing support.**
    ```
  - [x] Update all references to "therapy" and "mental health" throughout the document

- [x] **App icons and splash screen**
  - [x] Verify all app icons meet size requirements:
    - iOS: 1024x1024px (App Store), various sizes for device icons
    - Android: 512x512px (Play Store), adaptive icons for devices
  - [x] Ensure splash screen is properly configured in app.config.js
  - [x] Verify icons don't contain any medical or therapeutic imagery

- [ ] **App screenshots** (PENDING)
  - [ ] Create screenshots showing the app in use (not just title screens)
  - [ ] Ensure screenshots don't contain any prohibited terminology
  - [ ] Prepare different sizes:
    - iOS: 6.5" (1284x2778px), 5.5" (1242x2208px), 12.9" iPad Pro (2048x2732px)
    - Android: Phone (1080x1920px), 7" tablet (1200x1920px), 10" tablet (1920x1200px)
  - [ ] Include screenshots of key features:
    - [ ] Home screen with companion selection
    - [ ] Chat conversation in progress
    - [ ] Settings screen
    - [ ] Account management

- [ ] **Privacy policy**
  - [x] Update privacy policy at https://verbalyzeai.com/privacy.html
  - [ ] Ensure privacy policy URL is accessible and non-geofenced
  - [ ] Add clear section about AI-generated content
  - [ ] Include data collection, storage, and sharing practices
  - [ ] Add section about user data rights and deletion

- [ ] **Age rating**
  - [ ] Set appropriate age rating (likely 12+ or 17+ due to open-ended AI conversations)
  - [ ] Ensure all app content is consistent with the chosen age rating
  - [ ] Complete questionnaires for both app stores accurately

## 🔒 Data Security and Privacy

- [ ] **Review data collection practices**
  - [ ] Audit all data collection to ensure compliance with privacy laws (GDPR, CCPA)
  - [ ] Implement proper consent mechanisms for data collection
  - [x] Ensure message encryption is properly implemented:
    - [x] Verify `src/lib/messageCrypto.ts` implementation
    - [x] Test encryption/decryption with production keys
    - [x] Ensure encryption key is securely stored

- [x] **Data storage and encryption**
  - [x] Verify secure storage of user conversations:
    - [x] Check `src/lib/encryptedStorageAdapter.ts` implementation
    - [x] Ensure `expo-secure-store` is properly configured
  - [ ] Review message encryption implementation:
    - [x] Verify `src/lib/messageCrypto.ts` implementation for encrypting/decrypting messages
    - [x] Confirm messages are encrypted before storing in the database
    - [x] Test decryption functionality works correctly
  - [x] Confirm encryption key handling is secure:
    - [x] Keep encryption key in `.env.development` for initial app submission
    - [x] Keep encryption key in `src/lib/envLoader.js` for initial app submission
    - [ ] Plan to set up proper key management using EAS secrets in a future update
  - [x] Document data retention and deletion policies:
    - [x] Add clear policies to privacy policy
    - [x] Implement account deletion functionality in `app/settings.tsx` (via <NAME_EMAIL>)
    - [x] Ensure account deletion also removes encrypted messages (handled manually by admin)

- [x] **Third-party services**
  - [x] Review all API keys in the codebase:
    - [x] Check `.env.development` for exposed keys
    - [x] Check `eas.json` for exposed keys
    - [x] Check `src/config/aiConfig.ts` for hardcoded keys
  - [x] Ensure API keys are properly managed:
    - [x] Keep API keys in environment variables for initial app submission
    - [ ] Plan to move API keys to EAS secrets in a future update
  - [x] Configure environment variables for production:
    ```json
    // In eas.json
    "production": {
      "env": {
        "EXPO_PUBLIC_AI_PROVIDER": "openrouter",
        "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free",
        "EXPO_PUBLIC_SUPABASE_URL": "https://tqnzwbergnfosjkfqyfx.supabase.co"
        // API keys stored in .env files for initial submission
      }
    }
    ```

## 📋 Legal Requirements

- [x] **Implement required disclaimers**
  - [x] Add a clear disclaimer in `app/index.tsx` and `app/settings.tsx` that follows Apple and Google guidelines:
    ```javascript
    This app uses artificial intelligence to generate responses for informational and educational purposes only. It is not intended to provide medical advice, diagnosis, or treatment. The information provided may be inaccurate or incomplete and should not be used as a substitute for professional care. By using this app, you agree that you are solely responsible for your personal decisions. If you are experiencing a crisis or need immediate assistance, please contact a licensed professional, call emergency services, or reach out to your local crisis hotline. Always consult with appropriate professionals for any health-related concerns.
    ```
  - [x] Ensure disclaimer is prominently displayed during first-time user experience
  - [x] Include disclaimer in app description for both app stores
  - [x] Add a reminder that users should consult with appropriate professionals for any health-related concerns (required by Apple's guidelines section 1.4.1)

- [ ] **Terms of service**
  - [x] Update terms of service at https://verbalyzeai.com/terms.html
  - [ ] Include clear sections on:
    - [ ] App limitations and non-medical nature
    - [ ] AI-generated content limitations and potential inaccuracies
    - [ ] User responsibilities and appropriate use
    - [ ] Explicit statement that the app does not provide medical or therapeutic advice
    - [ ] Data collection, storage, and sharing practices

- [x] **User consent**
  - [x] Implement explicit user consent for data collection on first launch
  - [x] Add consent for AI conversation storage with clear explanation
  - [x] Create clear opt-out mechanisms in settings
  - [x] Add option to reset privacy preferences and show consent dialog again
  - [x] Ensure account deletion functionality properly removes all user data (via email <NAME_EMAIL>)
  - [x] Provide a way for users to export their data (recommended for GDPR compliance)

## 🧪 Testing

- [ ] **Functional testing**
  - [ ] Test all app features on both iOS and Android:
    - [ ] User registration and login
    - [ ] Companion selection
    - [ ] Chat functionality
    - [ ] Settings and preferences
    - [ ] Account management
  - [ ] Verify app behavior in offline mode:
    - [ ] Appropriate error messages
    - [ ] Graceful degradation of features
    - [ ] Data persistence and recovery
  - [ ] Test all user flows and navigation:
    - [x] Onboarding experience
    - [x] Main navigation paths
    - [x] Edge cases (e.g., no companions available)

- [ ] **Compliance testing**
  - [x] **Verify all instances of prohibited terminology have been removed**
  - [x] Search codebase for terms like "therapy", "therapist", "mental health", etc.
  - [x] Check database queries for references to old table and field names
  - [x] Review UI text for any missed terminology
  - [ ] Verify disclaimers are properly displayed
  - [x] Verify privacy policy, terms of service, and account deletion links work

- [ ] **Performance testing**
  - [ ] Test on lower-end devices:
    - [ ] Android: Entry-level devices with limited RAM
    - [ ] iOS: Older iPhone models (iPhone 8/SE)
  - [ ] Monitor battery usage:
    - [ ] During active chat sessions
    - [ ] Background usage
    - [ ] Idle state
  - [ ] Test memory usage:
    - [ ] During extended conversations
    - [ ] With multiple chat sessions
    - [ ] After prolonged app use

- [ ] **Security testing**
  - [ ] Perform security audit:
    - [ ] Check for data leakage
    - [ ] Verify authentication mechanisms
    - [ ] Test session management
  - [ ] Test encryption implementation:
    - [ ] Verify message encryption/decryption
    - [ ] Test with production keys
    - [ ] Ensure secure key storage
  - [ ] Verify secure API communications:
    - [ ] Check for proper HTTPS usage
    - [ ] Verify API key handling
    - [ ] Test error handling for API failures

## 🔄 EAS Build Tasks

- [x] **Configure EAS for production builds**
  - [x] Update eas.json with proper production configuration:
    ```json
    "production": {
      "autoIncrement": true,
      "android": {
        "buildType": "app-bundle"
      },
      "env": {
        "EXPO_PUBLIC_AI_PROVIDER": "openrouter",
        "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free"
      }
    }
    ```
  - [ ] Plan to set up EAS secrets for sensitive environment variables in a future update:
    ```bash
    # Set up secrets for production builds (FUTURE UPDATE)

    # OpenRouter API Key (required for AI functionality)
    eas secret:create --scope project --name EXPO_PUBLIC_OPENROUTER_API_KEY --value "your-api-key"

    # Supabase Configuration (required for authentication and database)
    eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_URL --value "https://tqnzwbergnfosjkfqyfx.supabase.co"
    eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_ANON_KEY --value "your-supabase-anon-key"

    # Message Encryption Key (required for secure message storage)
    # Generate a secure random key using: openssl rand -base64 24
    eas secret:create --scope project --name EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY --value "your-encryption-key"

    # Optional: DeepSeek API Key (if using DeepSeek as AI provider)
    eas secret:create --scope project --name EXPO_PUBLIC_DEEPSEEK_API_KEY --value "your-deepseek-api-key"
    ```

    Note: For initial app submission, API keys are stored in environment variables.

    Important notes about EAS secrets:
    - Secrets are encrypted and stored securely in EAS servers
    - They are injected into the build environment during the build process
    - They are not stored in the app bundle or source code
    - They can be updated without rebuilding the app
    - Use `eas secret:list` to view all secrets
    - Use `eas secret:delete` to remove a secret
  - [x] Configure auto-increment for build numbers in eas.json

- [x] **iOS-specific configuration**
  - [x] Verify iOS bundle identifier in app.config.js:
    ```javascript
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.redpre.verbalyze",
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
        UIBackgroundModes: [
          "remote-notification"
        ]
      },
      usesAppleSignIn: false,
      usesIcloudStorage: false,
      config: {
        usesNonExemptEncryption: false
      }
    },
    ```
  - [x] Configure iOS permissions in app.config.js
  - [x] Verify encryption compliance settings:
    - [x] Confirm `usesNonExemptEncryption` is set to `false` (this is correct since you're using standard encryption algorithms)
    - [x] Be prepared to answer Apple's encryption questions during submission:
      - "Does your app use encryption?" - Yes
      - "Does your app qualify for any of the exemptions provided in Category 5, Part 2 of the U.S. Export Administration Regulations?" - Yes
      - "Does your app implement any encryption algorithms that are proprietary or not accepted as standards by international standard bodies?" - No
      - "Does your app implement any standard encryption algorithms instead of or in addition to using or accessing the encryption in Apple's operating system?" - Yes (CryptoJS)
  - [x] Set up App Store Connect information in eas.json:
    ```json
    "submit": {
      "production": {
        "ios": {
          "appleId": "YOUR_APPLE_ID",
          "ascAppId": "YOUR_APP_STORE_CONNECT_APP_ID",
          "appleTeamId": "YOUR_APPLE_TEAM_ID"
        }
      }
    }
    ```

- [x] **Android-specific configuration**
  - [x] Verify Android package name in app.config.js:
    ```javascript
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#012B40"
      },
      package: "com.redpre.verbalyze"
    },
    ```
  - [x] Configure Android permissions in app.config.js
  - [x] Set up Android build type to app-bundle in eas.json
  - [x] Set up Google Play Store credentials in eas.json:
    ```json
    "submit": {
      "production": {
        "android": {
          "track": "production"
        }
      }
    }
    ```

- [ ] **Build commands**
  ```bash
  # For iOS production build
  eas build --platform ios --profile production

  # For Android production build
  eas build --platform android --profile production

  # For both platforms
  eas build --platform all --profile production
  ```

- [ ] **Submission commands**
  ```bash
  # For iOS submission
  eas submit --platform ios

  # For Android submission
  eas submit --platform android

  # Alternative: Build and submit in one command
  eas build --platform ios --profile production --auto-submit
  eas build --platform android --profile production --auto-submit
  ```

- [ ] **App Store metadata**
  - [ ] Set up app store metadata using EAS Metadata:
    ```bash
    # Initialize metadata
    eas metadata:init

    # Push metadata to stores
    eas metadata:push
    ```

## 🔍 Final Review

- [x] **Content review**
  - [x] Check all text content for prohibited terminology:
    - [x] Search codebase for "therapy", "therapist", "mental health", "medical"
    - [x] Review all user-facing strings
    - [x] Check database field names and table names
  - [x] Review all images and assets for compliance:
    - [x] Ensure no medical symbols or imagery
    - [x] Verify app icon doesn't suggest medical services
    - [x] Check companion images for professional medical appearance
  - [x] Verify all links work correctly:
    - [x] Privacy policy (https://verbalyzeai.com/privacy.html)
    - [x] Terms of service (https://verbalyzeai.com/terms.html)
    - [x] Account deletion (https://verbalyzeai.com/delete-account.html)
    - [x] Support contact

- [ ] **Compliance check**
  - [ ] Verify compliance with Apple App Store guidelines:
    - [ ] Review [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/) section 1.4.1 (Medical apps)
    - [ ] Check [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/) section 5.1.1 (Privacy)
  - [ ] Verify compliance with Google Play Store guidelines:
    - [ ] Review [Health Content and Services](https://support.google.com/googleplay/android-developer/answer/********) guidelines
    - [ ] Check [User Data policy](https://support.google.com/googleplay/android-developer/answer/********)
  - [ ] Check compliance with data protection regulations:
    - [ ] GDPR (EU users)
    - [ ] CCPA (California users)
    - [ ] COPPA (if applicable for users under 13)

- [ ] **Documentation**
  - [ ] Prepare responses for potential review questions:
    - [ ] "How does your app use AI?"
    - [ ] "Does your app provide medical or mental health services?"
    - [ ] "How do you protect user data?"
  - [ ] Document all compliance measures taken:
    - [ ] Create a compliance checklist with completed items
    - [ ] Document all terminology changes
    - [ ] Keep record of all disclaimer additions
  - [ ] Create a list of all third-party libraries used:
    - [ ] Review package.json for dependencies
    - [ ] Document purpose of each library
    - [ ] Verify licenses for all libraries

## 📝 Post-Submission

- [ ] **Monitor review status**
  - [ ] Check App Store Connect for iOS review status:
    - [ ] Set up email notifications for status changes
    - [ ] Check daily for any reviewer questions
  - [ ] Check Google Play Console for Android review status:
    - [ ] Monitor for policy compliance issues
    - [ ] Watch for content rating changes
  - [ ] Be prepared to respond quickly to any reviewer questions:
    - [ ] Have team members on standby during review period
    - [ ] Prepare template responses for common questions

- [ ] **Prepare for updates**
  - [ ] Document the submission process for future updates:
    - [ ] Create step-by-step guide for team members
    - [ ] Document all credentials and access requirements
  - [ ] Set up monitoring for app performance and crashes:
    - [ ] Implement crash reporting
    - [ ] Set up analytics for key user flows
  - [ ] Plan for regular updates to address any issues:
    - [ ] Create a schedule for regular maintenance updates
    - [ ] Prioritize any issues identified during review

---

## Important Notes

1. **Medical App Guidelines**: Both app stores have specific guidelines for apps that could be interpreted as providing medical or health-related services:
   - Apple's App Store Review Guidelines section 1.4.1 states that "Medical apps that could provide inaccurate data or information, or that could be used for diagnosing or treating patients may be reviewed with greater scrutiny."
   - Google Play's Health Content and Services policy requires apps to "not feature medical or health-related functionalities that are misleading or potentially harmful."

2. **Disclaimer Requirements**: Both app stores require clear disclaimers for apps that provide health-related information:
   - ✅ Disclaimers should state that the app is not a substitute for professional medical care
   - ✅ Include reminders to consult with appropriate professionals for health-related concerns
   - ✅ These should be prominent in the app and in all marketing materials

3. **AI Content Disclosure**: Be transparent about AI-generated content and its limitations:
   - ✅ Clearly state that responses are generated by AI and may not always be accurate or appropriate
   - ✅ Explain the limitations of AI in providing personalized advice
   - ✅ Disclose that AI models may occasionally produce incorrect or misleading information

4. **Data Privacy**: Clearly document all data collection, storage, and sharing practices:
   - Ensure users can easily access, export, and delete their data
   - Comply with relevant privacy regulations (GDPR, CCPA)
   - Provide clear opt-out mechanisms for data collection

5. **Message Encryption**: The app now encrypts all chat messages before storing them in the database:
   - ✅ Ensure the encryption key is properly managed and not hardcoded
   - ✅ Document the encryption method in the privacy policy
   - ✅ Be prepared to answer questions about encryption during app review
   - ✅ For iOS, properly declare encryption usage in App Store Connect

6. **Terminology Considerations**: While not strictly prohibited, using terms like "therapy," "therapist," or "mental health" may trigger additional scrutiny:
   - ✅ Consider using alternatives like "conversation," "companion," "emotional wellbeing," and "personal growth"
   - ✅ If you do use these terms, ensure you have appropriate disclaimers and don't make medical claims
   - ✅ Be prepared for a potentially longer review process if using health-related terminology

7. **Regular Updates**: Plan for regular updates to address any compliance issues that may arise after submission:
   - App store policies change frequently, so staying current is essential
   - Monitor for policy updates related to AI and health apps

8. **API Keys and Secrets**: Never include actual API keys or secrets in production builds:
   - Use EAS secrets or other secure methods to manage sensitive credentials
   - Implement proper key rotation and management practices
