import CryptoJS from 'crypto-js';
import { env } from '../config/env';

/**
 * Gets the encryption key from environment
 * @returns The encryption key from the environment
 */
const getEncryptionKey = (): string => {
  const key = env.encryption.key;
  if (!key) {
    console.error('EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY is not defined. Encryption cannot proceed.');
    throw new Error('Encryption key is not available. Ensure EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY is set.');
  }
  return key;
};

/**
 * Encrypts a plaintext message using a very simple custom method.
 * This is NOT secure encryption, but it will work without any native dependencies.
 * @param plaintext The message to encrypt.
 * @returns A promise that resolves to the encrypted text.
 */
export async function encryptMessage(plaintext: string): Promise<string> {
  if (!plaintext) return '';

  // ULTRA SIMPLE APPROACH: No crypto libraries at all

  try {
    // Get the key from the environment
    const key = getEncryptionKey();

    // Very simple custom "encryption" - this is just obfuscation, not real encryption
    // But it will work without any native dependencies
    const simpleEncrypt = (text: string, encKey: string): string => {
      // Create a simple key by repeating the key to match the text length
      const repeatedKey = encKey.repeat(Math.ceil(text.length / encKey.length))
                               .substring(0, text.length);

      // XOR each character with the corresponding key character and convert to hex
      let result = '';
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i) ^ repeatedKey.charCodeAt(i % repeatedKey.length);
        result += charCode.toString(16).padStart(4, '0');
      }

      // Add a prefix to identify our custom encryption
      return 'CUSTOM:' + result;
    };

    return simpleEncrypt(plaintext, key);
  } catch (error) {
    console.error('Custom encryption failed:', error);
    throw error;
  }
}

/**
 * Decrypts an encrypted message back to plaintext.
 * @param ciphertext The encrypted message.
 * @returns A promise that resolves to the decrypted plaintext message.
 */
export async function decryptMessage(ciphertext: string): Promise<string> {
  if (!ciphertext) return '';

  // ULTRA SIMPLE APPROACH: No crypto libraries at all

  // Check if this is our custom encrypted message
  if (ciphertext.startsWith('CUSTOM:')) {
    try {
      // Extract the encrypted content
      const encryptedContent = ciphertext.substring(7); // Remove 'CUSTOM:' prefix

      // Get the key from environment
      const key = getEncryptionKey();

      // Simple custom decryption function
      const simpleDecrypt = (encrypted: string, decKey: string): string => {
        // Convert hex back to character codes
        const codes = [];
        for (let i = 0; i < encrypted.length; i += 4) {
          // Only process complete 4-character chunks
          if (i + 4 <= encrypted.length) {
            const hexChunk = encrypted.substring(i, i + 4);
            // Only push if we have a valid hex string
            if (/^[0-9a-fA-F]{4}$/.test(hexChunk)) {
              codes.push(parseInt(hexChunk, 16));
            }
          }
        }

        // If no valid codes were found, return empty string
        if (codes.length === 0) return '';

        // Create a repeated key to match the length of the codes
        const repeatedKey = decKey.repeat(Math.ceil(codes.length / decKey.length))
                                 .substring(0, codes.length);

        // XOR each code with the corresponding key character to get the original character
        let result = '';
        for (let i = 0; i < codes.length; i++) {
          const originalChar = String.fromCharCode(
            codes[i] ^ repeatedKey.charCodeAt(i % repeatedKey.length)
          );
          result += originalChar;
        }

        return result;
      };

      return simpleDecrypt(encryptedContent, key);
    } catch (error) {
      console.error('Custom decryption failed:', error);
      throw error;
    }
  }

  // If not our custom encrypted message, try to decrypt with CryptoJS
  try {
    // Get the key from environment
    const key = getEncryptionKey();

    const bytes = CryptoJS.AES.decrypt(ciphertext, key);
    const plaintext = bytes.toString(CryptoJS.enc.Utf8);

    if (plaintext) {
      return plaintext;
    }
  } catch (error) {
    console.error('Decryption failed:', error);
    throw error;
  }

  // If all decryption methods fail, return a placeholder
  return '[Encrypted message]';
}

/**
 * Initializes the encryption system.
 * Call this early in your app's lifecycle to ensure encryption is ready.
 */
export async function initializeEncryption(): Promise<void> {
  try {
    // Verify that the encryption key is available
    const key = getEncryptionKey();
    // console.log('Encryption initialized with key from environment');
  } catch (error) {
    console.error('Failed to initialize encryption:', error);
    throw new Error('Encryption initialization failed. Please ensure EXPO_PUBLIC_MESSAGE_ENCRYPTION_KEY is set in your environment.');
  }
}