import { aiConfig, AIProvider } from './config';
import { Part, GenerateContentResponse, GenerationConfig, Content, SafetySetting } from '@google/genai';

// Local fallback types due to persistent lint errors with SDK exports
interface LocalGenerateContentRequest {
  contents: Content[];
  safetySettings?: SafetySetting[];
  generationConfig?: GenerationConfig;
}

interface LocalGenerateContentStreamResult {
  stream: AsyncIterable<GenerateContentResponse>;
  response: Promise<GenerateContentResponse>;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionOptions {
  messages: ChatMessage[];
  stream?: boolean; // Note: React Native streaming might require additional handling depending on UI needs
  onProviderFallback?: (provider: string) => void;
}

export interface ChatCompletionResponse {
  content: string;
  providerUsed: string;
}

/**
 * AI Client for generating chat completions in React Native
 */
export class AIClient {
  async generateChatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { provider } = aiConfig;
    const fallbackChain: AIProvider[] = [
      'openrouter',
      'deepseek',
      'lmstudio',
      'ollama'
    ];

    // Remove the primary provider from fallback chain if it's there
    const remainingFallbacks = fallbackChain.filter(p => p !== provider);
    const allProvidersToTry = [provider, ...remainingFallbacks];

    let lastError: Error | null = null;

    for (const currentProvider of allProvidersToTry) {
      try {
        // Notify about fallback if it's not the first attempt
        if (currentProvider !== provider && options.onProviderFallback) {
          options.onProviderFallback(currentProvider);
        }

        console.log(`Attempting to use AI provider: ${currentProvider}`);
        
        let result: ChatCompletionResponse;
        
        switch (currentProvider) {
          case 'ollama':
            result = await this.generateOllamaCompletion(options);
            break;
          case 'lmstudio':
            result = await this.generateLMStudioCompletion(options);
            break;
          case 'deepseek':
            result = await this.generateDeepseekCompletion(options);
            break;
          case 'openrouter':
            result = await this.generateOpenrouterCompletion(options);
            break;
          case 'gemini':
            result = await this.generateGeminiCompletion(options);
            break;
          default:
            const _exhaustiveCheck: never = currentProvider;
            throw new Error(`Unsupported AI provider: ${currentProvider}`);
        }

        // If we get here, the provider worked
        return {
          ...result,
          providerUsed: currentProvider
        };
      } catch (error) {
        console.error(`Provider ${currentProvider} failed:`, error);
        lastError = error as Error;
        // Continue to next provider in the chain
      }
    }

    // If we get here, all providers failed
    console.error('All AI providers failed:', lastError);
    throw lastError || new Error('All AI providers failed');
  }

  private async generateOllamaCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { baseUrl, model } = aiConfig.ollamaConfig;

    const response = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model,
        messages: options.messages,
        stream: options.stream || false, // Pass stream option
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Ollama API error (${response.status}): ${errorText}`);
      throw new Error(`Ollama API error (${response.status})`);
    }

    const data = await response.json();
    // Ollama's non-streaming response structure
    const content = data.message?.content;

    if (typeof content !== 'string') {
      console.error('Invalid Ollama response structure:', data);
      throw new Error('No valid content in Ollama response');
    }

    return { content, providerUsed: 'ollama' };
  }

  private async generateLMStudioCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { baseUrl, model } = aiConfig.lmstudioConfig;



    try {
      const response = await fetch(`${baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: model, // LMStudio might need the specific model identifier loaded
          messages: options.messages,
          stream: options.stream || false,
          temperature: 0.7, // Add temperature parameter
          max_tokens: 1000, // Add max_tokens parameter
        }),
      });



      if (!response.ok) {
        const errorText = await response.text();
        console.error(`LMStudio API error (${response.status}): ${errorText}`);
        throw new Error(`LM Studio API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();


      const content = data.choices?.[0]?.message?.content;

      if (typeof content !== 'string') {
        console.error('Invalid LMStudio response structure:', data);
        throw new Error('No valid content in LMStudio response');
      }


      return { content, providerUsed: 'lmstudio' };
    } catch (error) {
      console.error('Error in LM Studio completion:', error);
      throw error;
    }
  }

  private async generateDeepseekCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { apiKey, model } = aiConfig.deepseekConfig;

    if (!apiKey) {
      throw new Error('DeepSeek API key is not configured. Set DEEPSEEK_API_KEY in .env');
    }

    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: options.messages,
        stream: options.stream || false,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`DeepSeek API error (${response.status}): ${errorText}`);
      throw new Error(`DeepSeek API error (${response.status})`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (typeof content !== 'string') {
      console.error('Invalid DeepSeek response structure:', data);
      throw new Error('No valid content in DeepSeek response');
    }

    return { content, providerUsed: 'deepseek' };
  }

    private async generateOpenrouterCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { apiKey, model, baseUrl } = aiConfig.openrouterConfig;


    if (!apiKey) {
       throw new Error('OpenRouter API key is not configured. Set OPENROUTER_API_KEY in .env');
    }

    // In React Native, 'Referer' and 'X-Title' might not be necessary or standard.
    // Check OpenRouter docs if they are strictly required for mobile clients.
    // For now, let's omit them unless issues arise.
    const headers: HeadersInit = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    };

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        model,
        messages: options.messages,
        stream: options.stream || false,
        // Consider adding 'route': 'fallback' or other OpenRouter specific params if needed
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`OpenRouter API error (${response.status}): ${errorText}`);
      throw new Error(`OpenRouter API error (${response.status})`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (typeof content !== 'string') {
      console.error('Invalid OpenRouter response structure:', data);
      throw new Error('No valid content in OpenRouter response');
    }

    return { content, providerUsed: 'openrouter' };
  }

  private async generateGeminiCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    const { apiKey, model: modelName } = aiConfig.geminiConfig; // Renamed model to modelName to avoid conflict
    if (!apiKey) {
      throw new Error('Gemini API key is not configured.');
    }

    // Define the API endpoint. Ensure 'gemini-pro-flash' is the correct model identifier for the API.
    // The documentation uses 'gemini-pro-flash', but your config might have a more specific one like 'gemini-1.5-flash-latest'
    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent`;

    try {
      // Transform messages to Gemini's Content format
      // System messages are prepended to the first user message or sent as a separate user message if no user message exists first.
      let systemMessageContent = '';
      const geminiContents: Content[] = [];

      options.messages.forEach((msg, index) => {
        if (msg.role === 'system') {
          systemMessageContent += msg.content + '\n'; // Accumulate system messages
          return; // Skip adding system message directly to geminiContents here
        }

        let currentMessageText = msg.content;
        // If it's the first non-system message and there's system content, prepend it.
        if (systemMessageContent && (geminiContents.length === 0 || geminiContents.every(c => c.role !== 'user'))) {
          currentMessageText = systemMessageContent.trim() + '\n\n' + currentMessageText;
          systemMessageContent = ''; // Clear after prepending
        }

        geminiContents.push({
          role: msg.role === 'assistant' ? 'model' : 'user', // Gemini uses 'model' for assistant
          parts: [{ text: currentMessageText }],
        });
      });
      
      // If there was only a system message, send it as a user message.
      if (systemMessageContent && geminiContents.length === 0) {
        geminiContents.push({ role: 'user', parts: [{ text: systemMessageContent.trim() }] });
      }

      if (geminiContents.length === 0) {
        throw new Error('No messages to send to Gemini.');
      }

      const generationConfig: GenerationConfig = {
        maxOutputTokens: 8192,
        temperature: 0.7,
        // topP and topK can be added if needed
        // responseMimeType: 'text/plain', // This is often default, can be omitted unless specific format needed
      };

      const safetySettings: SafetySetting[] = [
        { category: 'HARM_CATEGORY_HARASSMENT' as any, threshold: 'BLOCK_MEDIUM_AND_ABOVE' as any },
        { category: 'HARM_CATEGORY_HATE_SPEECH' as any, threshold: 'BLOCK_MEDIUM_AND_ABOVE' as any },
        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any, threshold: 'BLOCK_MEDIUM_AND_ABOVE' as any },
        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, threshold: 'BLOCK_MEDIUM_AND_ABOVE' as any },
      ];

      const requestBody = {
        contents: geminiContents,
        generationConfig,
        safetySettings,
      };

      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': apiKey,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error('Gemini API Error Response:', errorBody);
        throw new Error(`Gemini API request failed: ${response.status} - ${errorBody}`);
      }

      const data = await response.json();

      if (data.promptFeedback && data.promptFeedback.blockReason) {
        throw new Error(`Gemini API request blocked: ${data.promptFeedback.blockReason} - ${data.promptFeedback.blockReasonMessage || 'No specific message.'}`);
      }

      if (data && data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
        const aggregatedContent = data.candidates[0].content.parts.map((part: Part) => part.text || '').join('');
        return { content: aggregatedContent, providerUsed: 'gemini' };
      } else {
         // Check for finishReason if no content
        const finishReason = data?.candidates?.[0]?.finishReason;
        if (finishReason && finishReason !== 'STOP' && finishReason !== 'MAX_TOKENS') {
          throw new Error(`Gemini generation finished due to: ${finishReason}. No content returned.`);
        }
        throw new Error('No response content from Gemini API or unexpected format.');
      }

    } catch (error) {
      console.error('Error in Gemini completion:', error);
      if (error instanceof Error) {
        // Append more details if it's a known error structure from the API call
        throw new Error(`Gemini API error: ${error.message}`);
      }
      throw new Error('Unknown error in Gemini completion.');
    }
  }
}

// Export a singleton instance of the AI client
export const aiClient = new AIClient();

// Default export for easier imports
export default aiClient;
