import 'dotenv/config';

// Environment-aware configuration

export default {
  name: 'Verbalyze',
  slug: 'verbalyze',
  version: '2.5',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#012B40'
  },
  assetBundlePatterns: [
    '**/*'
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.redpre.verbalyze',
    buildNumber: '2',
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      UIBackgroundModes: [
        'remote-notification'
      ]
    },
    usesAppleSignIn: false,
    usesIcloudStorage: false,
    config: {
      usesNonExemptEncryption: false
    }
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#012B40'
    },
    package: 'com.redpre.verbalyze',
    versionCode: 3,
    permissions: [
      'android.permission.INTERNET'
    ],
    kotlinVersion: '1.9.23',
    compileSdkVersion: 34,
    targetSdkVersion: 34,
    minSdkVersion: 21
  },
  web: {
    favicon: './assets/favicon.png'
  },
  plugins: [
    'expo-router',
    'expo-updates',
    [
      'expo-build-properties',
      {
        android: {
          buildToolsVersion: '34.0.0',
          compileSdkVersion: 34,
          targetSdkVersion: 34,
          minSdkVersion: 21,
          kotlinCompilerExtensionVersion: '1.3.2',
          kotlinCompilerVersion: '1.7.20',
          extraPropertiesInGradle: {
            "android.kotlinCompilerExtensionVersion" : "1.3.2",
            "kotlin.suppressKotlinVersionCompatibilityCheck": true
          }
        },
        ios: {
          deploymentTarget: '15.1'
        }
      }
    ]
  ],
  extra: {
    eas: {
      projectId: '02d6e293-f42b-4d1c-a7f0-70fa9e04cc43'
    }
  },
  updates: {
    fallbackToCacheTimeout: 0,
    url: 'https://u.expo.dev/02d6e293-f42b-4d1c-a7f0-70fa9e04cc43',
    enabled: true,
    checkAutomatically: 'ON_LOAD'
  },
  runtimeVersion: {
    policy: 'appVersion'
  },
  description: "Verbalyze is a private, AI-powered chat companion that supports personal growth and emotional wellbeing through secure conversations. This app uses artificial intelligence to generate responses for informational and educational purposes only. It is not intended to provide medical advice, diagnosis, or treatment.",
  privacy: "unlisted"
};
