# Android Submission Guide for Verbalyze

This document provides step-by-step instructions for building and submitting the Verbalyze Android app to the Google Play Store.

## Prerequisites

1. **Google Play Developer Account**: You need an active Google Play Developer account ($25 one-time fee).
2. **App Setup in Google Play Console**: The app should be created in the Google Play Console with basic information filled out.
3. **Expo Account**: You need an Expo account with access to the project.
4. **EAS CLI**: Install the EAS CLI globally: `npm install -g eas-cli`

## Build and Submit Process

### Option 1: Using the Automated Script

We've created a script to simplify the build and submission process:

```bash
# Make the script executable
chmod +x build-android.sh

# Run the script
./build-android.sh
```

The script will:
1. Check if EAS CLI is installed and install it if needed
2. Log you into your Expo account
3. Build the Android app using the `android-production` profile
4. Ask if you want to submit the build to Google Play Store
5. If yes, it will submit the latest build to Google Play

### Option 2: Manual Process

If you prefer to run the commands manually:

1. **Login to EAS**:
   ```bash
   eas login
   ```

2. **Build the Android App**:
   ```bash
   eas build --platform android --profile android-production
   ```

3. **Submit to Google Play Store**:
   ```bash
   eas submit -p android --latest
   ```

## Configuration Files

The build and submission process uses the following configuration files:

- **eas.json**: Contains build profiles and submission settings
- **app.config.js**: Contains app metadata and Android-specific configuration
- **google-services.json**: Firebase configuration file (placeholder for now)

## Android Build Profile

We've created a specific build profile for Android submission in `eas.json`:

```json
"android-production": {
  "extends": "production",
  "android": {
    "buildType": "app-bundle",
    "gradleCommand": ":app:bundleRelease"
  },
  "env": {
    "EXPO_PUBLIC_AI_PROVIDER": "openrouter",
    "EXPO_PUBLIC_OPENROUTER_MODEL": "deepseek/deepseek-chat-v3-0324:free",
    "EXPO_PUBLIC_SUPABASE_URL": "https://tqnzwbergnfosjkfqyfx.supabase.co"
  },
  "channel": "production"
}
```

## Google Play Store Submission Settings

The submission settings in `eas.json` are configured as follows:

```json
"submit": {
  "production": {
    "android": {
      "track": "production",
      "releaseStatus": "completed"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check the EAS build logs for specific error messages
   - Ensure all dependencies are properly installed
   - Verify that the Android configuration in app.config.js is correct

2. **Submission Failures**:
   - Verify your Google Play Developer account is active
   - Ensure the app has been created in Google Play Console
   - Check that the app's package name matches in both app.config.js and Google Play Console

3. **Version Code Issues**:
   - Each submission to Google Play requires a higher version code
   - We've enabled `autoIncrement: true` in the build profile to handle this automatically

### Getting Help

If you encounter issues not covered here:

1. Check the [EAS Build documentation](https://docs.expo.dev/build/introduction/)
2. Check the [EAS Submit documentation](https://docs.expo.dev/submit/introduction/)
3. Visit the [Expo forums](https://forums.expo.dev/) for community support
