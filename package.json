{"name": "cozy-therapist-mobile", "version": "1.0.1", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@google/genai": "^1.4.0", "@hookform/resolvers": "^4.1.3", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@supabase/supabase-js": "^2.49.1", "@types/styled-components": "^5.1.34", "@types/styled-components-react-native": "^5.2.5", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "expo": "~52.0.46", "expo-build-properties": "~0.13.3", "expo-constants": "~17.0.8", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.19", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "expo-updates": "~0.27.4", "i18next": "^25.2.1", "metro": "^0.81.3", "metro-react-native-babel-preset": "^0.77.0", "mime": "^4.0.7", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.3", "react-native": "0.76.9", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-controller": "^1.17.3", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "styled-components": "^6.1.17", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/crypto-js": "^4.2.2", "@types/istanbul-reports": "^3.0.4", "@types/node": "^24.0.1", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}