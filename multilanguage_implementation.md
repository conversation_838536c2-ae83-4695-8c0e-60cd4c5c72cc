
# Multilingual Implementation Plan - FINALIZED

## Overview
This document outlines the comprehensive implementation plan for adding multilingual support to the Verbalyze mobile app. The goal is to allow users to select their preferred language from the settings screen and have the UI display in that language.

## Requirements
- Support for 6 languages: English (current), Albanian, German, Italian, French, Spanish
- Language selection through settings screen
- Persistent language preference across app sessions
- Only UI text changes (no changes to AI responses, content, or functionality)
- Follow existing app patterns (Context API, Expo Secure Store)

## Technical Approach - FINALIZED

### ✅ Recommended Libraries
- **react-i18next**: Main internationalization library for React Native
  - ✅ Excellent choice for React Native/Expo
  - ✅ Mature, well-maintained, and widely adopted
  - ✅ Perfect integration with React hooks
  - ✅ Compatible with Expo SDK 52
- **expo-localization**: For detecting device locale
  - ✅ Native Expo solution, already included in SDK
  - ✅ Provides `getLocales()` for device language detection

### 📁 Updated File Structure
```
src/
├── i18n/
│   ├── index.ts                 # Main i18n configuration
│   ├── resources.ts             # Translation resource imports
│   └── locales/
│       ├── en/
│       │   ├── common.json      # Common UI elements
│       │   ├── auth.json        # Authentication screens
│       │   ├── chat.json        # Chat interface
│       │   ├── settings.json    # Settings screen
│       │   └── welcome.json     # Welcome/onboarding
│       ├── sq/                  # Albanian
│       ├── de/                  # German
│       ├── it/                  # Italian
│       ├── fr/                  # French
│       └── es/                  # Spanish
└── contexts/
    └── LanguageContext.tsx      # Language state management
```

### 🔧 Implementation Steps

#### **Step 1: Install Dependencies**
```bash
npx expo install expo-localization
npm install react-i18next i18next
```

#### **Step 2: Create Language Context (Following Existing Theme Pattern)**
Create `src/contexts/LanguageContext.tsx` following the same pattern as `ThemeContext.tsx`:
```typescript
// src/contexts/LanguageContext.tsx
import React, { createContext, useState, useContext, useEffect, useMemo } from 'react';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { i18n } from '../i18n';
import { AVAILABLE_LANGUAGES } from '../i18n/resources';

// Language context interface
interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  availableLanguages: typeof AVAILABLE_LANGUAGES;
  t: typeof i18n.t;
}

// Create the context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  availableLanguages: AVAILABLE_LANGUAGES,
  t: i18n.t,
});

// Provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState('en');

  // Load language preference from storage
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        let storedLanguage: string | null = null;
        
        if (Platform.OS === 'web') {
          storedLanguage = localStorage.getItem('language');
        } else {
          storedLanguage = await SecureStore.getItemAsync('language');
        }
        
        if (storedLanguage) {
          setLanguage(storedLanguage);
          i18n.changeLanguage(storedLanguage);
        }
      } catch (error) {
        console.error('Failed to load language preference:', error);
      }
    };
    
    loadLanguage();
  }, []);

  // Change language and save preference
  const changeLanguage = async (lang: string) => {
    setLanguage(lang);
    i18n.changeLanguage(lang);
    
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem('language', lang);
      } else {
        await SecureStore.setItemAsync('language', lang);
      }
    } catch (error) {
      console.error('Failed to save language preference:', error);
    }
  };

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    language,
    setLanguage: changeLanguage,
    availableLanguages: AVAILABLE_LANGUAGES,
    t: i18n.t,
  }), [language]);

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => useContext(LanguageContext);
```

#### **Step 3: Configure i18next**
Create `src/i18n/index.ts` with:
```typescript
// src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { resources } from './resources';

i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3', // Required for React Native
    resources,
    lng: 'en', // Default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    react: {
      useSuspense: false, // React Native doesn't support Suspense yet
    },
  });

export { i18n };
```

#### **Step 4: Update Settings Screen**
Integrate into existing `app/settings.tsx`:
```typescript
// In app/settings.tsx
import { useLanguage } from '../src/contexts/LanguageContext';

export default function SettingsScreen() {
  // Existing code...
  const { language, setLanguage, availableLanguages } = useLanguage();
  
  // Add to the settings UI
  // This would go in the settings section, after the appearance section
  return (
    // Existing code...
    <View style={[styles.section, { backgroundColor: colors.card }]}>
      <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
        Language
      </Text>
      {availableLanguages.map((lang) => (
        <TouchableOpacity
          key={lang.code}
          style={[
            styles.settingItem,
            { borderBottomColor: colors.border }
          ]}
          onPress={() => setLanguage(lang.code)}
        >
          <Text style={[styles.settingText, { color: colors.text }]}>
            {lang.nativeName} ({lang.name})
          </Text>
          {language === lang.code && (
            <Ionicons name="checkmark" size={22} color={colors.primary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
    // Existing code...
  );
}
```

#### **Step 5: Create Translation Files**
Organize translations by feature/screen:
```typescript
// src/i18n/resources.ts
export const AVAILABLE_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'sq', name: 'Albanian', nativeName: 'Shqip' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
];

// Import all translation files
import en_common from './locales/en/common.json';
import en_auth from './locales/en/auth.json';
import en_chat from './locales/en/chat.json';
import en_settings from './locales/en/settings.json';
import en_welcome from './locales/en/welcome.json';

// Define resources structure
export const resources = {
  en: {
    common: en_common,
    auth: en_auth,
    chat: en_chat,
    settings: en_settings,
    welcome: en_welcome,
  },
  // Other languages will be added similarly
};

// TypeScript type definitions for translation keys
export type TranslationNamespaces = 'common' | 'auth' | 'chat' | 'settings' | 'welcome';
```

Sample translation file:
```json
// src/i18n/locales/en/common.json
{
  "buttons": {
    "continue": "Continue",
    "cancel": "Cancel",
    "save": "Save"
  },
  "navigation": {
    "back": "Back",
    "next": "Next"
  },
  "status": {
    "loading": "Loading...",
    "error": "Something went wrong"
  }
}
```

#### **Step 6: Migrate Existing Strings**
Gradual migration approach:
- Replace hardcoded strings with `useTranslation` hook
- Start with critical screens (auth, settings)
- Use descriptive, hierarchical keys
- Example: `auth.login.welcomeBack`, `common.buttons.continue`

## 🎯 Key Improvements

1. **Better Architecture**: Using Context API pattern already familiar in codebase
2. **Namespace Organization**: Splitting translations by feature/screen for better maintainability
3. **Expo Secure Store**: Consistent with existing storage patterns (not AsyncStorage)
4. **TypeScript Support**: Full type safety for translation keys
5. **Gradual Migration**: No need to translate everything at once
6. **Performance Optimization**: Using `useMemo` to prevent unnecessary re-renders

## TypeScript Support

```typescript
// src/i18n/types.ts
export type TranslationKeys = {
  common: {
    buttons: {
      continue: string;
      cancel: string;
      save: string;
    };
    navigation: {
      back: string;
      next: string;
    };
    status: {
      loading: string;
      error: string;
    };
  };
  // Other namespaces with their keys
};
```

### Key Naming Convention
- Use descriptive, hierarchical keys
- Group by functionality/screen
- Examples:
  - `common.buttons.continue`
  - `auth.login.welcomeBack`
  - `settings.language.title`
  - `chat.input.placeholder`

## Persistence Strategy - FINALIZED
- ✅ Use **Expo Secure Store** (not AsyncStorage) for consistency with existing app patterns
- ✅ Platform-specific storage (SecureStore for native, localStorage for web)
- ✅ Load preference on app startup in LanguageContext
- ✅ Fall back to English if no stored preference

## 🧪 Testing Strategy
1. **Language Switching**: Test all language combinations in settings
2. **Persistence**: Verify language preference survives app restarts
3. **Fallbacks**: Test missing translation keys fall back to English
4. **UI Layout**: Ensure text fits properly in all languages

### Testing Utilities
```typescript
// src/i18n/test-utils.ts
import { render } from '@testing-library/react-native';
import { LanguageProvider } from '../contexts/LanguageContext';
import { i18n } from './index';

export const renderWithI18n = (component, language = 'en') => {
  i18n.changeLanguage(language);
  return render(
    <LanguageProvider initialLanguage={language}>
      {component}
    </LanguageProvider>
  );
};
```

## 📋 Migration Priority
1. **Phase 1**: Core infrastructure and settings screen
2. **Phase 2**: Authentication screens (`app/auth.tsx`)
3. **Phase 3**: Welcome/onboarding screens (`app/welcome.tsx`)
4. **Phase 4**: Chat interface (`src/components/ChatInput.tsx`)
5. **Phase 5**: Remaining screens and components

## ⚠️ Important Considerations
1. **App Store Compliance**: Using standard language names should be fine for app store policies
2. **Bundle Size**: Each language adds ~10-20KB, which is minimal impact
3. **RTL Support**: None of target languages require RTL, so no additional complexity needed
4. **Performance**: i18next is optimized and won't impact app performance
5. **Maintenance**: Translation files will need updates when new UI text is added

## 🚀 Implementation Readiness
- ✅ Plan reviewed and approved
- ✅ Compatible with existing Expo SDK 52 and React Native 0.76.9
- ✅ Follows established app patterns (Context API, Expo Secure Store)
- ✅ Gradual migration approach allows for incremental implementation
- ✅ No breaking changes to existing functionality
- ✅ Performance optimized with React best practices

## Next Steps
1. **Begin with Step 1: Install dependencies**
2. **Implement LanguageContext following ThemeContext pattern**
3. **Create basic translation files for English**
4. **Integrate language selection into settings screen**
5. **Begin gradual migration of hardcoded strings**

**Status**: APPROVED - Ready for implementation