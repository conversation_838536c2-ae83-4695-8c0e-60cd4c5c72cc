import { useEffect, useRef } from 'react';
import { supabase } from '../lib/supabase';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { Message } from '../types/chat';
import { useAuth } from '../contexts/AuthContext';
import { decryptMessage } from '../lib/messageCrypto';

export const useChatSubscription = (
  sessionId: string | null,
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
) => {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const { session, loading: authLoading } = useAuth();

  // Function to get more detailed error information
  const getDetailedErrorInfo = (err: any): string => {
    if (!err) return 'No error object provided';
    
    // Check for Supabase specific error properties
    if (err.error) {
      return `Supabase Error: ${err.error.message || JSON.stringify(err.error)}`;
    }
    
    // Check for WebSocket specific errors
    if (err.type === 'websocket') {
      return `WebSocket Error: ${err.message || 'Unknown WebSocket error'}`;
    }
    
    // Check for network errors
    if (err.message?.includes('NetworkError') || err.message?.includes('Failed to fetch')) {
      return `Network Error: ${err.message}`;
    }
    
    // Fallback to stringifying the error
    return typeof err === 'object' ? JSON.stringify(err) : String(err);
  };

  // Subscribe to the channel once and rely on Supabase's built-in reconnection.
  // Calling `channel.subscribe()` more than once on the same channel instance
  // throws an error ("subscribe can only be called a single time per channel instance").
  // This simplified helper guarantees a single subscribe call.
  const subscribeSafely = (channel: RealtimeChannel) => {
    channel.subscribe((status, err) => {
      // Ignore non-fatal "CHANNEL_ERROR" events that Supabase occasionally emits
      if (status === 'SUBSCRIBED' || status === 'CHANNEL_ERROR') {
        return;
      }

      const errorMessage = err ? getDetailedErrorInfo(err) : 'No error details available';
      console.warn(`Subscription status (${status}) on channel ${channel.topic}:`, errorMessage);
    });
  };

  // Function to check and delete empty session if needed
  const checkAndDeleteEmptySession = async (sessionId: string) => {
    try {
      // Check if session has any messages
      const { count, error: countError } = await supabase
        .from('chat_messages')
        .select('*', { count: 'exact', head: true })
        .eq('session_id', sessionId);

      if (!countError && count === 0) {
        // If no messages, delete the session
        await supabase
          .from('chat_sessions')
          .delete()
          .eq('id', sessionId);
      }
    } catch (error) {
      console.warn('Error checking/deleting empty session:', error);
    }
  };

  useEffect(() => {
    // Store the current session ID for cleanup
    const currentSessionId = sessionId;

    // Cleanup function that will run when the component unmounts or session changes
    return () => {
      if (currentSessionId) {
        // Check and delete empty session in the background
        void checkAndDeleteEmptySession(currentSessionId);
      }
    };
  }, [sessionId]);

  useEffect(() => {
    // Wait for auth loading to finish and ensure we have a session ID and a valid session
    if (authLoading || !sessionId || !session) {
      // Clean up existing channel if conditions are not met (e.g., user logs out)
      if (channelRef.current) {
        try {
          const channelToRemove = channelRef.current;
          channelRef.current = null; // Clear the ref first to prevent race conditions
          void supabase.removeChannel(channelToRemove);
        } catch (error) {
          // console.error('Error removing channel during cleanup:', getDetailedErrorInfo(error));
        }
      }
      return undefined;
    }

    // Set up channel name for this session
    const channelName = `chat-messages-${sessionId}`;

    // Check if we're already subscribed to this exact channel
    if (channelRef.current && channelRef.current.topic === channelName) {
      return undefined;
    }

    // Clean up any existing channel first
    const cleanupChannel = async () => {
      if (channelRef.current) {
        try {
          const channelToRemove = channelRef.current;
          channelRef.current = null; // Clear the ref first to prevent race conditions
          await new Promise<void>((resolve) => {
            try {
              void supabase.removeChannel(channelToRemove);
              // Add a small delay to ensure cleanup completes
              setTimeout(resolve, 100);
            } catch (error) {
              console.error('Error during channel cleanup:', getDetailedErrorInfo(error));
              resolve();
            }
          });
        } catch (error) {
          console.error('Error removing channel:', getDetailedErrorInfo(error));
        }
      }
    };

    void cleanupChannel().then(() => {
      // Create and store the new channel with proper configuration
      try {
        channelRef.current = supabase.channel(channelName, {
          config: {
            broadcast: { self: true }, // Enable receiving your own broadcasts
            presence: { key: sessionId }
          },
        });

        if (!channelRef.current) {
          console.error('Failed to create channel: channel is null');
          return;
        }

        channelRef.current
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'chat_messages',
            filter: `session_id=eq.${sessionId}`
          }, (payload) => {
            if (payload.new && typeof payload.new === 'object' && 'id' in payload.new) {
              const newMessage = payload.new as Message;

              setMessages((prevMessages) => {
                if (prevMessages.some(msg => msg.id === newMessage.id)) {
                  return prevMessages;
                }

                // For AI messages that we've already displayed with the typewriter effect,
                // we don't need to decrypt them again - just keep the existing message
                const existingMessage = prevMessages.find(msg =>
                    msg.id === newMessage.id &&
                    msg.content &&
                    msg.content.length > 0 &&
                    !msg.content.startsWith('[Encrypted') &&
                    !msg.content.startsWith('Companion is responding'));

                if (newMessage.is_companion && existingMessage) {
                  return prevMessages;
                }

                // Check if we already have a placeholder for this message
                const placeholderIndex = prevMessages.findIndex(msg =>
                  msg.id === newMessage.id &&
                  (msg.content === 'Companion is responding...' ||
                   msg.content === 'Loading message...' ||
                   msg.content === '')
                );

                // If we already have a placeholder, we'll update it instead of adding a new message
                if (placeholderIndex !== -1) {
                  // Decrypt the message asynchronously and update the placeholder
                  decryptMessage(newMessage.content)
                    .then(decryptedContent => {
                      setMessages(currentMessages => {
                        return currentMessages.map(msg => {
                          if (msg.id === newMessage.id) {
                            return { ...msg, content: decryptedContent };
                          }
                          return msg;
                        });
                      });
                    })
                    .catch(error => {
                      console.error('Error decrypting message:', getDetailedErrorInfo(error));
                      setMessages(currentMessages => {
                        return currentMessages.map(msg => {
                          if (msg.id === newMessage.id) {
                            return { ...msg, content: '[Encrypted message]' };
                          }
                          return msg;
                        });
                      });
                    });

                  return prevMessages; // Return unchanged, we'll update it in the async callback
                }

                // For new messages without a placeholder, decrypt immediately and add once
                decryptMessage(newMessage.content)
                  .then(decryptedContent => {
                    setMessages(currentMessages => {
                      // Double-check the message doesn't already exist
                      if (currentMessages.some(msg => msg.id === newMessage.id)) {
                        return currentMessages;
                      }

                      return [...currentMessages, {
                        ...newMessage,
                        content: decryptedContent
                      }];
                    });
                  })
                  .catch(error => {
                    console.error('Error decrypting message:', getDetailedErrorInfo(error));
                    setMessages(currentMessages => {
                      // Double-check the message doesn't already exist
                      if (currentMessages.some(msg => msg.id === newMessage.id)) {
                        return currentMessages;
                      }

                      return [...currentMessages, {
                        ...newMessage,
                        content: '[Encrypted message]'
                      }];
                    });
                  });

                return prevMessages; // Return unchanged, we'll add it in the async callback
              });
            }
          })
          // Subscribe to the channel using the safe helper
          subscribeSafely(channelRef.current);
      } catch (error) {
        console.error('Error creating channel:', getDetailedErrorInfo(error));
      }
    });

    // Cleanup function
    return () => {
      if (channelRef.current) {
        try {
          const channelToRemove = channelRef.current;
          channelRef.current = null; // Clear the ref first to prevent resubscription
          
          // Add a small delay before removing the channel to ensure any pending operations complete
          setTimeout(() => {
            try {
              supabase.removeChannel(channelToRemove)
                .then(() => {
                })
                .catch((error) => {
                });
            } catch (error) {
              console.error('Error during channel removal:', getDetailedErrorInfo(error));
            }
          }, 100);
        } catch (error) {
          console.error('Error during cleanup:', getDetailedErrorInfo(error));
        }
      }
    };
  }, [sessionId, session, authLoading, setMessages]);
};