import React, { memo, useMemo, useCallback } from 'react'; // Added useCallback import
import { 
  View, 
  Text, 
  Image, 
  StyleSheet, 
  StyleProp, 
  ViewStyle, 
  ImageSourcePropType, 
  ImageStyle, 
  TextStyle 
} from 'react-native';
import { format } from 'date-fns';
import { Ionicons } from '@expo/vector-icons';
import Markdown, { MarkdownProps } from 'react-native-markdown-display';
import { Message } from '../types/chat';
import { COLORS } from '../lib/constants';
import { handleError, ErrorSource, ErrorSeverity } from '../utils/errorHandler';
import { useAppTranslation } from '../i18n';
// Define ThemeColors type if not available from theme context
interface ThemeColors {
  text: string;
  accent: string;
  background: string;
  card: string;
  cardBorder: string;
  textSecondary: string;
  white: string;
  [key: string]: any; // For any additional properties
}

// Extended Message type with backward compatibility
interface ExtendedMessage extends Message {
  is_user?: boolean; // For backward compatibility
}

interface MessageItemProps {
  item: ExtendedMessage;
  userImageSource: ImageSourcePropType;
  companionImageSource: ImageSourcePropType;
  colors: ThemeColors;
}

/**
 * MessageItem component displays a single chat message
 * Memoized to prevent unnecessary re-renders
 */
const MessageItem = memo(({
  item,
  userImageSource,
  companionImageSource,
  colors
}: MessageItemProps) => {
  // Determine if the message is from the user or companion
  const isUser = !item.is_companion;
  const profileImageSource = isUser ? userImageSource : companionImageSource;
  const { t } = useAppTranslation('chat');

  // Format the timestamp once
  const formattedTime = useMemo(() => {
    try {
      return format(new Date(item.created_at), 'h:mm a');
    } catch (error) {
      handleError(
        error as Error,
        ErrorSource.UI,
        false,
        ErrorSeverity.WARNING
      );
      return '';
    }
  }, [item.created_at]);

  // Memoize the markdown styles for companion messages
  const markdownStylesWithColor = useMemo(() => {
    const styles: MarkdownProps['style'] = {
      ...markdownStyles,
      body: {
        ...markdownStyles.body,
        color: colors.text
      }
    };
    return styles;
  }, [colors.text]);

  // Memoize the row style to prevent recreation on each render
  const rowStyle = useMemo<StyleProp<ViewStyle>>(
    () => [
      styles.messageRow,
      isUser ? styles.userMessageRow : styles.companionMessageRow,
      { backgroundColor: colors.background }
    ],
    [isUser, colors.background]
  );

  // Memoize the bubble style to prevent recreation on each render
  const bubbleStyle = useMemo<StyleProp<ViewStyle>>(
    () => [
      styles.messageBubble,
      isUser
        ? [styles.userMessageBubble, { backgroundColor: colors.accent }]
        : [
            styles.companionMessageBubble, 
            { 
              backgroundColor: colors.card,
              borderColor: colors.cardBorder 
            }
          ]
    ],
    [isUser, colors.accent, colors.card, colors.cardBorder]
  );

  // Memoize the time text style to prevent recreation on each render
  const timeTextStyle = useMemo<StyleProp<TextStyle>>(
    () => [
      styles.messageTime,
      isUser && styles.userMessageTime,
      { color: colors.textSecondary }
    ],
    [isUser, colors.textSecondary]
  );

  // Default image source
  const defaultImageSource = useMemo(
    () => require('../../assets/icon.png'),
    []
  );
  
  // Get the appropriate image source with fallback
  const getImageSource = useCallback((source: ImageSourcePropType = defaultImageSource) => {
    // If source is an array, take the first valid source
    if (Array.isArray(source)) {
      return source[0] || defaultImageSource;
    }
    return source || defaultImageSource;
  }, [defaultImageSource]);
  
  // Get the actual image source to use for companion messages
  const companionImageSourceToUse = useMemo(() => 
    getImageSource(companionImageSource)
  , [getImageSource, companionImageSource]);
  
  // Get the actual image source to use for user messages
  const userImageSourceToUse = useMemo(() => 
    getImageSource(userImageSource)
  , [getImageSource, userImageSource]);

  return (
    <View style={rowStyle}>
      {/* Profile Picture - Only for companion messages */}
      {!isUser && (
        <Image
          source={companionImageSourceToUse as any} // Type assertion to handle the union type
          style={styles.profilePic}
          defaultSource={defaultImageSource}
        />
      )}

      {/* Message Content Container */}
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessageContainer : styles.companionMessageContainer
      ]}>
        {/* Message Bubble */}
        <View style={bubbleStyle}>
          <Markdown 
            style={isUser ? {
              ...markdownStylesWithColor,
              body: {
                ...markdownStylesWithColor.body,
                color: colors.white, // Use white text for user messages
              },
            } : markdownStylesWithColor}
            mergeStyle={false}
            rules={{
              // Customize markdown rules if needed
            }}
          >
            {item.content}
          </Markdown>
        </View>
        {/* Time & Status - Only render if we have a valid time */}
        {formattedTime && (
          <View style={styles.timeStatusContainer}>
            <Text style={timeTextStyle}>
              {formattedTime}
            </Text>
            {isUser && (
              <Ionicons 
                name="checkmark-done-outline" 
                size={16} 
                color={colors.accent} 
                style={styles.statusIcon} 
              />
            )}
          </View>
        )}
      </View>

      {/* User Profile Picture (appears after message) */}
      {isUser && (
        <Image
          source={userImageSourceToUse as any} // Type assertion to handle the union type
          style={styles.profilePic}
          defaultSource={defaultImageSource}
        />
      )}
    </View>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to determine if the component should re-render
  const itemChanged = 
    prevProps.item.id !== nextProps.item.id ||
    prevProps.item.content !== nextProps.item.content ||
    prevProps.item.created_at !== nextProps.item.created_at;
    
  const colorsChanged = 
    prevProps.colors.text !== nextProps.colors.text ||
    prevProps.colors.accent !== nextProps.colors.accent ||
    prevProps.colors.background !== nextProps.colors.background ||
    prevProps.colors.card !== nextProps.colors.card ||
    prevProps.colors.cardBorder !== nextProps.colors.cardBorder ||
    prevProps.colors.textSecondary !== nextProps.colors.textSecondary ||
    prevProps.colors.white !== nextProps.colors.white;
    
  // Only re-render if the item data or relevant colors have changed
  return !(itemChanged || colorsChanged);
});

// Move styles outside the component to prevent recreation on each render
const styles = StyleSheet.create({
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: COLORS.white,
  },
  userMessageRow: {
    justifyContent: 'flex-end',
  },
  companionMessageRow: {
    justifyContent: 'flex-start',
  },
  profilePic: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginHorizontal: 0,
    marginLeft: 1,
    marginRight: 1,
  },
  messageContainer: {
    maxWidth: '75%',
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  companionMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingVertical: 10,
    // These paddings are for user messages
  },
  userMessageBubble: {
    backgroundColor: COLORS.primary,
    borderBottomRightRadius: 5,
  },
  companionMessageBubble: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.gray[200],
    borderBottomLeftRadius: 5,
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
  },
  userMessageText: {
    color: COLORS.white,
  },
  companionMessageText: {
    color: COLORS.text,
  },
  timeStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  messageTime: {
    fontSize: 11,
    color: COLORS.gray[500],
  },
  userMessageTime: {
    // Specific style for user time if needed
  },
  statusIcon: {
    marginLeft: 5,
  },
});

// Move markdown styles outside the component
const markdownStyles = StyleSheet.create({
  body: {
    fontSize: 15,
    lineHeight: 20,
    color: COLORS.text,
    paddingHorizontal: 3,  // Match the horizontal padding of messageBubble
    paddingVertical: 3,     // Match the vertical padding of messageBubble
    margin: 0,              // Reset any default margins
  },
  // Ensure other markdown elements don't add extra spacing
  paragraph: {
    marginTop: 0,
    marginBottom: 0,
  },
});

export default MessageItem;
